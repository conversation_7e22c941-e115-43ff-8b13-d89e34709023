import 'package:deewan/app/data/models/entities/identity_settings_model.dart';

import '../../../core/storage/objectbox_store.dart';
import '../../data/models/entities/app_settings_model.dart';
import '../services_interface/app_services_interface.dart';
import '../services_interface/objectbox_service_interface.dart'
    as objectbox_interface;

/// ObjectBox database service implementation
/// Handles database initialization, operations, and lifecycle management
class ObjectboxService extends InitializableService
    implements objectbox_interface.ObjectboxServiceAbstract {
  @override
  late ObjectBoxStore objectbox;

  @override
  Future<void> _init() async {
    await ObjectBoxStore.init();
    objectbox = ObjectBoxStore.instance;
  }

  @override
  Future<void> start() async {
    await super.start();
    // Additional start logic if needed
  }

  @override
  Future<void> stop() async {
    await super.stop();
    // Close database connections if needed
  }

  @override
  bool get isHealthy => super.isHealthy && isDatabaseReady;

  dynamic getBox<T>() {
    // Use ObjectBoxStore's getBox method
    return objectbox.getBox<T>();
  }

  Future<void> close() async {
    // Close ObjectBox instance
    await ObjectBoxStore.close();
  }

  @override
  dynamic get appSettingsBox => objectbox.getBox<AppSettings>();

  @override
  dynamic get identitySettingsBox =>  objectbox.getBox<IdentitySettings>(); // TODO: Implement when ObjectBox schema is updated

  @override
  Future<void> initializeDatabase() async {
    await _init();
  }

  @override
  Future<void> closeDatabase() async {
    await close();
  }

  @override
  bool get isDatabaseReady => isReady;

  @override
  Future<bool> backupDatabase(String path) async {
    // Implement backup logic
    return false; // Placeholder
  }

  @override
  Future<bool> restoreDatabase(String path) async {
    // Implement restore logic
    return false; // Placeholder
  }

  @override
  Map<String, dynamic> getDatabaseStats() {
    // Return database statistics
    return {}; // Placeholder
  }
}
